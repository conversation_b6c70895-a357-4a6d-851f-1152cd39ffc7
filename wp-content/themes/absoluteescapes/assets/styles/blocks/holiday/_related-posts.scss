.related-posts {
    position: relative;
    //background: $white;

    &:after {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: -500%;
        z-index: 0;
        width: 1000%;
        height: 100%;
        // background: inherit;
    }

    a {
        text-decoration: none;
        font-weight: normal;
    }

    &__inner {
        position: relative;
        z-index: 2;
        padding: 65px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding: 45px 0;
        }
    }


    &__container {
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 20px;
        }
    }

    &__content {
        max-width: 800px;

    }

    .inspiration__posts {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -15px 60px;
        padding-top: 30px;
    }

    .inspiration__post {
        flex: 0 0 50%;
        max-width: 50%;
        padding: 0 15px 30px;
        border-bottom: none;

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }



    .holidays__posts {
        display: flex;
        flex-wrap: wrap;
        padding-top: 30px;
        margin: 0 -15px;
    }

    .holidays__post {
        flex: 0 0 50%;
        max-width: 50%;
        padding: 0 15px;
        border-bottom: 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            flex: 0 0 100%;
            max-width: 100%;
        }

        a {
            &:hover, &:focus {
                .holidays__title {
                    color: $teal;
                }
            }
        }
    }

    .holidays__title {
        transition: 300ms;
    }

    .holidays__image {
        img {
            width: 100%;
        }
    }

    .flickity-button {
        display: block;
        width: 20px;
        height: 20px;
        border: none;
        padding: 0;
        opacity: 0;

        svg {
            display: block;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            fill: $white;
        }

        &.previous {
            left: 4px;
        }

        &.next {
            right: 4px;
        }
    }

    .flickity-page-dots {
        bottom: 10px;

        .dot {
            width: 4px;
            height: 4px;
            margin: 0 2px;

            &.is-selected {
                width: 6px;
                height: 6px;
            }
        }
    }

    .holidays__gallery {
        max-width: 100%;
        &:hover {
            .flickity-button {
                opacity: 1;
            }
        }
    }

    .holidays__post-content {
        padding-top: 15px;
    }

    .holidays__price {
        margin-bottom: 20px;
    }

    .marker__items {
        padding: 5px 0 0;
    }

    .marker__item {
        display: inline-block;
        vertical-align: middle;
        margin-right: 18px;
        margin-bottom: 7px;

        &:last-child {
            margin-right: 0;
        }
    }
}
