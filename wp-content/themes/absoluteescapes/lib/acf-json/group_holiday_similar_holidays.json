{"key": "group_holiday_similar_holidays", "title": "Holiday - Similar Holidays", "fields": [{"key": "field_similar_holidays_group", "label": "Similar Holidays", "name": "similar_holidays", "aria-label": "", "type": "group", "instructions": "Manage the 'Similar holidays' section that appears at the bottom of holiday pages. If no holidays are selected, the system will automatically show 2 random holidays from the same holiday type.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_similar_holidays_manual_selection", "label": "Manual Selection", "name": "manual_selection", "aria-label": "", "type": "true_false", "instructions": "Enable to manually select specific holidays. When disabled, holidays will be automatically selected from the same holiday type.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "Manual", "ui_off_text": "Automatic", "ui": 1}, {"key": "field_similar_holidays_selected", "label": "Selected Holidays", "name": "selected_holidays", "aria-label": "", "type": "repeater", "instructions": "Select up to 4 holidays to display in the similar holidays section. They will be displayed in a 2-column grid on desktop and 1-column on mobile.", "required": 0, "conditional_logic": [[{"field": "field_similar_holidays_manual_selection", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "min": 1, "max": 4, "layout": "table", "button_label": "Add Holiday", "collapsed": "", "rows_per_page": 20, "sub_fields": [{"key": "field_similar_holidays_holiday", "label": "Holiday", "name": "holiday", "aria-label": "", "type": "post_object", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["holiday"], "taxonomy": "", "return_format": "object", "multiple": 0, "allow_null": 0, "ui": 1, "bidirectional_target": []}]}]}], "location": [[{"param": "post_type", "operator": "==", "value": "holiday"}]], "menu_order": 10, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1751646080}